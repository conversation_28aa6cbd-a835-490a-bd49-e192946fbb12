# Tooltip Scroll Issue Fix

## Problem
Clicking a tooltip (specifically the AI tooltip in the Header) and then closing it caused page scrolling to stop working. This was happening because the tooltip was being treated as a "regular" modal by the scroll manager, which applies `position: fixed` to the body to prevent scrolling during modal display.

## Root Cause
The `scrollManager.ts` utility has a `detectModalType()` function that determines whether a component should be treated as a "regular" modal (which prevents background scrolling) or a "tooltip" modal (which allows background scrolling). 

The Header AI tooltip was being classified as a "regular" modal, causing these issues:
1. When tooltip opens: Body gets `position: fixed` applied, preventing scrolling
2. When tooltip closes: Scroll manager tries to restore scroll position, but this restoration process can fail or cause scroll to become disabled

## Solution
Added 'Header' to the list of tooltip components in `scrollManager.ts`:

```typescript
// Before
const tooltipComponents = ['TourGuide'];

// After  
const tooltipComponents = ['TourGuide', 'Header'];
```

## Files Changed
1. `src/utils/scrollManager.ts` - Added 'Header' to tooltip components list
2. `src/components/Header.tsx` - Updated comment to reflect correct detection
3. `src/utils/scrollManager.test.ts` - Added test case for Header tooltip detection

## Verification
The fix ensures that:
- ✅ Header AI tooltip no longer prevents page scrolling
- ✅ TourGuide tooltip continues to work correctly  
- ✅ Regular modals (Modal, ClientModal, etc.) still prevent scrolling as expected
- ✅ Scroll position is preserved correctly for all modal types

## Testing
To test the fix:
1. Open the application
2. Click the AI sparkle button in the header to open the tooltip
3. Verify you can still scroll the page while tooltip is open
4. Close the tooltip by clicking outside or the close button
5. Verify page scrolling continues to work normally

## Technical Details
The scroll manager now correctly identifies Header tooltips as "tooltip" type, which means:
- No `position: fixed` is applied to the body
- Background scrolling remains enabled
- No complex scroll position restoration is needed
- Page scroll functionality is preserved

This fix maintains the existing behavior for all other components while specifically addressing the Header tooltip scroll issue.
