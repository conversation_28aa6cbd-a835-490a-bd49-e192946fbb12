Prompt 1:

i need to generate a backend from the front end. api should be in node js using express js, running on port 3000.
It should be a typescript codebase. It should have models, routes, services, middleware validations.
The models should be client model with imageUrl and name for the client name, and brand model with imageUrl, and name for the brand name.

we want to use sequelize and postgresql for the database.

Prompt 2
1: yes, include id, name, logo, description, and enabled columns

2: include id, name, logo, endpoint, we do not need isDefault

3: One client can have many brands

4: open for now. use cors

5: we are using aws s3 to store the files

6: we have postgres installed, aws rds for production

7: yes, create .env

8: good

create a new server folder that should house the server code
