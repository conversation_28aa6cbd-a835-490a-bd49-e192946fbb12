[default]
aws_access_key_id = somekey
aws_secret_access_key = somekey2

mkdir -p ~/.aws/projects/
nano ~/.aws/projects/scoot

mv ~/.aws/credentials ~/.aws/projects/eliso
cp ~/.aws/projects/scoot ~/.aws/credentials

# frontend deployment

aws s3 sync dist/ s3://marketing-demo-bt --delete --acl public-read

aws cloudfront create-invalidation --distribution-id E27DB18T5N2GCZ --paths "/\*"

# AWS INFO

AWS Cloudfront Interface: https://us-east-1.console.aws.amazon.com/cloudfront/v4/home?region=us-east-1#/distributions/E27DB18T5N2GCZ
Cloudfront: https://dq9ad3o0d9vx3.cloudfront.net/balsam
to point to: marketing-demo.bloodandtreasure.com
AWS S3 interface: https://us-east-1.console.aws.amazon.com/s3/buckets/marketing-demo-bt?region=us-east-1&tab=objects&bucketType=general
S3: s3://bt-marketing-demo
AWS API Interface: https://us-east-1.console.aws.amazon.com/ec2/home?region=us-east-1#InstanceDetails:instanceId=i-02e0af95e0964027a
Identifier: marketing-demo-api
Elastic IP Address: **************
AWS DB Interface: https://us-east-1.console.aws.amazon.com/rds/home?region=us-east-1#database:id=marketing-demo-db;is-cluster=false
Identifier: marketing-demo-db
Host: marketing-demo-db.chth7m5hjcpr.us-east-1.rds.amazonaws.com
User: root
Pass: hajk_sd-ASLK.JK890,123
Name: marketingdemo
AWS Sign in Console: https://872840119104.signin.aws.amazon.com/console
User: eliso
Pass: ghaks=cdo-8y7.dqyg,j2
AWS Credentials for CLI:
Key: ********************
Secret: fKdYL7ygvmf8+CK7eC+vMQBD5WHjxYPs9eBJ1xni (edited)
