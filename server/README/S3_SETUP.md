# S3 Setup Guide for Scoot Insights

This guide will help you set up AWS S3 for file storage in the Scoot Insights application.

## Prerequisites

1. An AWS account
2. AWS CLI installed (optional but recommended)
3. Basic knowledge of AWS IAM and S3

## Step 1: Create an S3 Bucket

1. Log into your AWS Console
2. Navigate to S3 service
3. Click "Create bucket"
4. Choose a unique bucket name (e.g., `scoot-insights-files`)
5. Select your preferred region (e.g., `us-east-1`)
6. Configure bucket settings:
   - **Block Public Access**: Keep all blocks enabled for security
   - **Bucket Versioning**: Optional (recommended for production)
   - **Tags**: Add tags for organization (optional)
7. Click "Create bucket"

## Step 2: Create an IAM User

1. Navigate to IAM service in AWS Console
2. Click "Users" → "Create user"
3. Enter a username (e.g., `scoot-insights-s3-user`)
4. Select "Programmatic access"
5. Click "Next: Permissions"

## Step 3: Attach S3 Policy

1. Click "Attach existing policies directly"
2. Click "Create policy"
3. Use the JSON tab and paste this policy:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:PutObject",
        "s3:GetObject",
        "s3:DeleteObject",
        "s3:ListBucket",
        "s3:PutObjectAcl"
      ],
      "Resource": [
        "arn:aws:s3:::YOUR-BUCKET-NAME",
        "arn:aws:s3:::YOUR-BUCKET-NAME/*"
      ]
    }
  ]
}
```

4. Replace `YOUR-BUCKET-NAME` with your actual bucket name
5. Name the policy (e.g., `ScootInsightsS3Access`)
6. Create the policy
7. Go back to user creation and attach this policy
8. Complete user creation

## Step 4: Get Access Credentials

1. After creating the user, you'll see the Access Key ID and Secret Access Key
2. **IMPORTANT**: Download the CSV file or copy these credentials immediately
3. You won't be able to see the Secret Access Key again

## Step 5: Configure Environment Variables

1. Copy `env.example` to `.env` in the server directory
2. Update the following variables:

```env
# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_access_key_id_here
AWS_SECRET_ACCESS_KEY=your_secret_access_key_here
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-bucket-name-here
```

## Step 6: Configure S3 Bucket for Public Read Access

Since we're storing logos that need to be publicly accessible:

1. Go to your S3 bucket
2. Click "Permissions" tab
3. Under "Block public access", click "Edit"
4. Uncheck "Block all public access" (or just uncheck "Block public access to buckets and objects granted through new access control lists (ACLs)")
5. Save changes
6. Add this bucket policy (replace `YOUR-BUCKET-NAME`):

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::YOUR-BUCKET-NAME/*"
    }
  ]
}
```

## Step 7: Test the Configuration

1. Start your server: `npm run dev`
2. Check the console output for S3 status
3. You should see: "✅ S3 is properly configured and connected"
4. Test file upload through the admin interface

## Troubleshooting

### Common Issues:

1. **"S3 is not configured"**

   - Check that all environment variables are set
   - Ensure no typos in variable names

2. **"S3 connection failed"**

   - Verify your AWS credentials are correct
   - Check that the IAM user has the correct permissions
   - Ensure the bucket name is correct

3. **"Access Denied" errors**

   - Verify the bucket policy allows public read access
   - Check that the IAM policy includes all necessary permissions

4. **Files not uploading**
   - Check the server logs for specific error messages
   - Verify the bucket exists and is in the correct region

### Security Best Practices:

1. **Never commit `.env` files** to version control
2. **Use IAM roles** instead of access keys in production
3. **Regularly rotate** access keys
4. **Monitor S3 usage** through AWS CloudTrail
5. **Set up bucket logging** for audit purposes

## Production Considerations

1. **Use IAM Roles** instead of access keys when deploying to AWS
2. **Enable CloudFront** for better performance and caching
3. **Set up bucket lifecycle policies** to manage old files
4. **Enable versioning** for file recovery
5. **Monitor costs** through AWS Cost Explorer

## API Endpoints

Once configured, you can use these endpoints:

- `POST /api/upload/logo` - Upload logo files
- `PUT /api/upload/logo/replace` - Replace existing logos
- `DELETE /api/upload/logo` - Delete logo files
- `GET /api/upload/status` - Check S3 connection status

## Support

If you encounter issues:

1. Check the server logs for detailed error messages
2. Verify your AWS configuration using the AWS CLI
3. Test S3 access manually using the AWS Console
4. Review the troubleshooting section above
