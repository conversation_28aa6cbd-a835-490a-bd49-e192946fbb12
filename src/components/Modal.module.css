/* Modal Component CSS Module */

.modalOverlay {
  position: fixed;
  inset: 0;
  z-index: 100;
  overflow-y: auto;
  transition: opacity var(--transition-base);
}

.modalOverlay.open {
  opacity: 1;
}

.modalOverlay.closed {
  opacity: 0;
}

.modalBackdrop {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
  transition: opacity var(--transition-base);
}

.modalContainer {
  display: flex;
  min-height: 100vh;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
}

.modalContent {
  position: relative;
  width: 100%;
  max-width: 672px; /* max-w-2xl equivalent */
  transform: scale(1);
  overflow: hidden;
  border-radius: var(--radius-xl);
  background: white;
  box-shadow: var(--shadow-2xl);
  transition: all var(--transition-base);
}

.modalContent.animatingIn {
  transform: scale(1);
  opacity: 1;
}

.modalContent.animatingOut {
  transform: scale(0.95);
  opacity: 0;
}

.modalHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-100);
}

.modalTitle {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

.modalCloseButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2);
  background: none;
  border: none;
  border-radius: var(--radius-lg);
  color: var(--color-text-muted);
  cursor: pointer;
  transition: all var(--transition-base);
}

.modalCloseButton:hover {
  background: var(--color-gray-100);
  color: var(--color-text-secondary);
}

.modalCloseButton:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.modalCloseIcon {
  height: 1.25rem;
  width: 1.25rem;
  color: var(--color-text-muted);
}

.modalBody {
  padding: var(--spacing-6);
}

/* Size variants */
.small {
  max-width: 400px;
}

.medium {
  max-width: 672px; /* max-w-2xl */
}

.large {
  max-width: 896px; /* max-w-4xl */
}

.extraLarge {
  max-width: 1024px; /* max-w-5xl */
}

.full {
  max-width: calc(100vw - 2rem);
  max-height: calc(100vh - 2rem);
}

/* Modal variants */
.centered {
  align-items: center;
}

.top {
  align-items: flex-start;
  padding-top: var(--spacing-20);
}

.bottom {
  align-items: flex-end;
  padding-bottom: var(--spacing-20);
}

/* Content variants */
.noPadding .modalBody {
  padding: 0;
}

.compactPadding .modalBody {
  padding: var(--spacing-4);
}

.largePadding .modalBody {
  padding: var(--spacing-8);
}

/* Header variants */
.noHeader .modalHeader {
  display: none;
}

.compactHeader .modalHeader {
  padding: var(--spacing-4);
}

.largeHeader .modalHeader {
  padding: var(--spacing-8);
}

/* Animation variants */
.slideUp {
  animation: slideUpIn 0.3s ease-out forwards;
}

.slideDown {
  animation: slideDownIn 0.3s ease-out forwards;
}

.fadeIn {
  animation: fadeInScale 0.3s ease-out forwards;
}

@keyframes slideUpIn {
  from {
    transform: translateY(100px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes slideDownIn {
  from {
    transform: translateY(-100px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes fadeInScale {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modalContainer {
    padding: var(--spacing-2);
    align-items: flex-end;
  }
  
  .modalContent {
    max-width: 100%;
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  }
  
  .modalHeader {
    padding: var(--spacing-4);
  }
  
  .modalBody {
    padding: var(--spacing-4);
    max-height: 80vh;
    overflow-y: auto;
  }
  
  .full {
    max-width: 100vw;
    max-height: 100vh;
    border-radius: 0;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .modalContent {
    background: var(--color-gray-800);
    color: var(--color-text-inverse);
  }
  
  .modalHeader {
    border-bottom-color: var(--color-gray-700);
  }
  
  .modalTitle {
    color: var(--color-text-inverse);
  }
  
  .modalCloseButton {
    color: var(--color-gray-400);
  }
  
  .modalCloseButton:hover {
    background: var(--color-gray-700);
    color: var(--color-gray-300);
  }
}