import React, { useState, useEffect } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { Header } from "./Header";
import { Sidebar } from "./Sidebar";

import { Progress } from "../pages/Progress";
import { Timeline } from "../pages/Timeline";
import { Team } from "../pages/Team";
import { PersonaDetail } from "../pages/PersonaDetail";
import { Media } from "../pages/Media";
import { PersonaList } from "../pages/PersonaList";
import { ClientAdmin } from "../pages/ClientAdmin";
import { brandApiService } from "../services/brandApiService";
import type { Brand } from "../services/brandApiService";
import { TourGuide } from "./TourGuide";
import {
  shouldShowTour,
  getTourSteps,
  markTourCompleted,
} from "../services/tourService";
import { useTour } from "../contexts/TourContext";
import "../styles/tour.css";
import styles from "./Layout.module.css";

export function Layout() {
  const { brandName } = useParams<{ brandName: string }>();
  const [currentBrand, setCurrentBrand] = useState<Brand | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentClient, setCurrentClient] = useState<{
    name: string;
    logo: string;
  } | null>(null);


  const [activeView, setActiveView] = useState("progress");
  const [selectedPersonaId, setSelectedPersonaId] = useState<string | null>(
    null
  );
  const [activeSection, setActiveSection] = useState("progress");
  const [isCollapsed, setIsCollapsed] = useState(false);

  const location = useLocation();
  const navigate = useNavigate();
  const { showMainTour, startMainTour, closeMainTour } = useTour();

  // Load brand based on URL parameter
  useEffect(() => {
    const loadBrand = async () => {
      setLoading(true);
      setError(null);

      try {
        if (brandName) {
          const brand = await brandApiService.getBrandByEndpoint(brandName);
          if (brand) {
            setCurrentBrand(brand);
          } else {
            // If brand not found, redirect to default brand
            const defaultBrand = await brandApiService.getDefaultBrand();
            navigate(
              `/${defaultBrand.endpoint}${location.pathname.replace(
                `/${brandName}`,
                ""
              )}`
            );
          }
        } else {
          // No brand name in URL, get default brand
          const defaultBrand = await brandApiService.getDefaultBrand();
          setCurrentBrand(defaultBrand);
        }
      } catch (error) {
        console.error("Error loading brand:", error);
        setError("Failed to load brand information. Please try again.");
        // Fallback to a basic brand object to prevent complete failure
        setCurrentBrand({
          id: "fallback",
          name: "Project Roots",
          logo: "",
          endpoint: "fallback",
          clientId: "",
          isDefault: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      } finally {
        setLoading(false);
      }
    };

    loadBrand();
  }, [brandName, navigate, location.pathname]);

  // Load client information from brand data
  useEffect(() => {
    const loadClientInfo = () => {
      if (currentBrand?.client) {
        // Use client information directly from brand data
        setCurrentClient({
          name: currentBrand.client.name,
          logo: currentBrand.client.logo,
        });
      } else if (loading) {
        // Show loading state
        setCurrentClient(null);
      } else {
        // Fallback to default client info
        setCurrentClient({
          name: "Blood & Treasure",
          logo: "", // Will show initial 'B'
        });
      }
    };

    loadClientInfo();
  }, [currentBrand?.client, loading]);

  useEffect(() => {
    const path = location.pathname.split("/").slice(2).join("/") || "overview";
    setActiveSection(path || "overview");
    
    // Determine the main view based on the path
    if (["progress", "timeline", "team", "personas", "media"].includes(path) || !path || path === "overview") {
      setActiveView("overview");
    } else if (["ai-chatbot", "team-management", "workflow", "auto-reports", "analytics"].includes(path) || path === "admin") {
      setActiveView("admin");
    } else {
      setActiveView(path);
    }
  }, [location]);

  // Update CSS variable for sidebar width
  useEffect(() => {
    const sidebarWidth = isCollapsed ? "64px" : "256px";
    document.documentElement.style.setProperty("--sidebar-width", sidebarWidth);
  }, [isCollapsed]);

  // Check if tour should be shown
  useEffect(() => {
    if (shouldShowTour("main")) {
      startMainTour();
    }
  }, [startMainTour]);

  const handleTourComplete = () => {
    closeMainTour();
    markTourCompleted("main");
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const container = e.currentTarget;
    const sections = container.getElementsByTagName("section");
    let currentSection = "";

    for (const section of sections) {
      const rect = section.getBoundingClientRect();
      if (rect.top <= 100) {
        currentSection = section.id;
      }
    }

    if (currentSection) {
      setActiveSection(currentSection);
    }
  };

  const scrollToSection = (sectionId: string) => {
    const section = document.getElementById(sectionId);
    if (section) {
      const headerHeight = 64; // Header height in pixels (h-16 = 64px)
      const yOffset = -headerHeight - 16; // Add some extra spacing
      const yPosition =
        section.getBoundingClientRect().top + window.pageYOffset + yOffset;

      window.scrollTo({
        top: yPosition,
        behavior: "smooth",
      });
    }
  };

  const handleSectionChange = (sectionId: string) => {
    setActiveSection(sectionId);
    scrollToSection(sectionId);
  };

  const handlePersonaClick = (id: string) => {
    setSelectedPersonaId(id);
    setActiveView("persona-detail");
    navigate(`/${currentBrand?.endpoint}/personas/${id}`);
  };

  return (
    <div className={styles.container}>
      {/* Loading State */}
      {loading && (
        <div className={styles.overlay}>
          <div className={styles.overlayContent}>
            <div className={styles.spinner}></div>
            <p className={styles.loadingText}>Loading brand information...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && !loading && (
        <div className={styles.overlay}>
          <div className={styles.errorContainer}>
            <div className={styles.errorIcon}>⚠️</div>
            <h3 className={styles.errorTitle}>
              Error Loading Brand
            </h3>
            <p className={styles.errorMessage}>{error}</p>
            <button
              onClick={() => window.location.reload()}
              className={styles.retryButton}
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Fixed Sidebar - stays in place during scroll */}
      <Sidebar
        activeSection={activeSection}
        onSectionChange={handleSectionChange}
        isCollapsed={isCollapsed}
        setIsCollapsed={setIsCollapsed}
      />

      {/* Main Content Area - positioned to account for fixed sidebar and header */}
      <div
        className={`${styles.mainContent} ${
          isCollapsed ? styles.mainContentCollapsed : styles.mainContentExpanded
        }`}
      >
        {/* Fixed Header - stays on top during scroll */}
        <Header
          activeView={activeView}
          onViewChange={setActiveView}
          isCollapsed={isCollapsed}
        />

        {/* Scrollable Content Area - positioned below fixed header */}
        <div
          className={styles.scrollableContent}
          onScroll={handleScroll}
        >
          <div className={styles.contentContainer}>
            {activeView === "overview" && (
              <>
                <section id='timeline'>
                  <Timeline />
                </section>

                <section id='progress'>
                  <Progress />
                </section>

                <section id='team'>
                  <Team />
                </section>

                <section id='personas'>
                  <PersonaList onPersonaClick={handlePersonaClick} />
                </section>

                <section id='media'>
                  <Media />
                </section>
              </>
            )}
            {activeView === "admin" && (
              <section id='admin' className={styles.fadeIn}>
                <ClientAdmin />
              </section>
            )}
            {activeView === "persona-detail" && selectedPersonaId && (
              <section id='persona-detail' className={styles.fadeIn}>
                <PersonaDetail personaId={selectedPersonaId} />
              </section>
            )}
          </div>
        </div>
      </div>

      {/* Tour Guide */}
      {showMainTour && (
        <TourGuide
          steps={getTourSteps("main")}
          isOpen={showMainTour}
          onClose={closeMainTour}
          onComplete={handleTourComplete}
          tourId='main'
          currentBrand={currentBrand}
          onNavigate={handleSectionChange}
          isCollapsed={isCollapsed}
        />
      )}
    </div>
  );
}
