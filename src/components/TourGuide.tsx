import React, { useState, useEffect, useRef, useId } from "react";
import { X, ChevronLeft, ChevronRight, SkipForward } from "lucide-react";
import type { Brand } from "../services/brandApiService";
import { useOverlayState } from "../utils/overlayState";
import { useScrollManager } from "../utils/scrollManager";
import { scrollManager } from "../utils/scrollManager";
import styles from "./TourGuide.module.css";

// Helper function to render highlighted text with markdown-style bold markers
const renderHighlightedText = (text: string) => {
  const parts = text.split(/(\*\*.*?\*\*)/g);
  return parts.map((part, index) => {
    if (part.startsWith('**') && part.endsWith('**')) {
      // Remove the ** markers and apply highlighting
      const highlightedText = part.slice(2, -2);
      return (
        <span
          key={index}
          className={styles.highlightedText}
        >
          {highlightedText}
        </span>
      );
    }
    return part;
  });
};

export interface TourStep {
  id: string;
  target: string;
  title: string;
  content: string;
  position?: "top" | "bottom" | "left" | "right";
  action?: "click" | "hover" | "none";
  // Add navigation section ID for automatic navigation
  navigateTo?: string;
}

interface TourGuideProps {
  steps: TourStep[];
  isOpen: boolean;
  onClose: () => void;
  onComplete: () => void;
  tourId: string;
  currentBrand?: Brand | null;
  // Add navigation callback prop
  onNavigate?: (sectionId: string) => void;
  // Add sidebar state prop for boundary calculations
  isCollapsed?: boolean;
}

export function TourGuide({
  steps,
  isOpen,
  onClose,
  onComplete,
  tourId,
  currentBrand,
  onNavigate,
  isCollapsed = false,
}: TourGuideProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const modalId = useId(); // Generate unique ID for this modal instance
  
  // Register this tour with the overlay system
  useOverlayState(isOpen);
  
  // Use the scroll manager to preserve scroll position (TourGuide is detected as tooltip)
  useScrollManager(modalId, isOpen, 'TourGuide');

  // Helper function to detect if target is in sidebar navigation
  const isSidebarNavTarget = (target: HTMLElement): boolean => {
    return target.closest('.sidebar') !== null || 
           target.closest('[data-tour="nav"]') !== null ||
           target.closest('.nav-item') !== null;
  };

  useEffect(() => {
    if (!isOpen) return;

    const target = document.querySelector(
      steps[currentStep]?.target
    ) as HTMLElement;
    setTargetElement(target);

    if (target) {
      // Add highlight class to target element, but skip for header to avoid positioning issues
      if (!target.classList.contains("header")) {
        target.classList.add("tour-highlight");
      }

      // Check if target is in sidebar navigation
      const isSidebarTarget = isSidebarNavTarget(target);

      // Scroll target into view if needed
      target.scrollIntoView({
        behavior: "smooth",
        block: "center",
        inline: "center",
      });

      // For sidebar nav targets, temporarily prevent user scrolling
      // but allow programmatic scrolling (scrollIntoView)
      if (isSidebarTarget) {
        // Temporarily prevent user scrolling while allowing programmatic scrolling
        const originalOverflow = document.body.style.overflow;
        document.body.style.overflow = 'hidden';
        
        // Re-enable scrolling after scrollIntoView completes
        setTimeout(() => {
          document.body.style.overflow = originalOverflow;
        }, 1000);
      }

      // Auto-navigate if this step has a navigation target
      const step = steps[currentStep];
      if (step?.navigateTo && onNavigate) {
        // Small delay to ensure the target element is highlighted first
        setTimeout(() => {
          onNavigate(step.navigateTo!);
        }, 300);
      }
    }

    return () => {
      if (target) {
        target.classList.remove("tour-highlight");
      }
    };
  }, [currentStep, steps, isOpen, onNavigate]);

  useEffect(() => {
    if (!isOpen) {
      setCurrentStep(0);
      // Remove all tour highlights
      document.querySelectorAll(".tour-highlight").forEach((el) => {
        el.classList.remove("tour-highlight");
      });
    }
  }, [isOpen]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    onComplete();
  };

  if (!isOpen || !targetElement) return null;

  const step = steps[currentStep];
  const rect = targetElement.getBoundingClientRect();
  const tooltipRect = tooltipRef.current?.getBoundingClientRect();

  // Calculate position
  const getTooltipPosition = () => {
    // Special case for first step - center on page
    if (currentStep === 0) {
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      return {
        top: viewportHeight / 2 - 250, // Move higher on screen
        left: viewportWidth / 2 - 300, // Adjust for wider tooltip
      };
    }

    const position = step.position || "bottom";
    const margin = 12;

    let top = 0;
    let left = 0;

    switch (position) {
      case "top":
        top = rect.top - margin;
        left = rect.left + rect.width / 2;
        break;
      case "bottom":
        top = rect.bottom + margin;
        left = rect.left + rect.width / 2;
        break;
      case "left":
        top = rect.top + rect.height / 2;
        left = rect.left - margin;
        break;
      case "right":
        top = rect.top + rect.height / 2;
        left = rect.right + margin;
        break;
    }

    // Adjust for tooltip size
    if (tooltipRect) {
      switch (position) {
        case "top":
          top -= tooltipRect.height;
          left -= tooltipRect.width / 2;
          break;
        case "bottom":
          left -= tooltipRect.width / 2;
          break;
        case "left":
          top -= tooltipRect.height / 2;
          left -= tooltipRect.width;
          break;
        case "right":
          top -= tooltipRect.height / 2;
          break;
      }
    }

    // Define boundaries
    const headerHeight = 64; // Header height in pixels
    const sidebarWidth = isCollapsed ? 64 : 256; // Sidebar width based on state
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Ensure tooltip stays within viewport and respects header/sidebar boundaries
    const minLeft = sidebarWidth + 20; // Minimum left position (sidebar width + margin)
    const maxLeft = viewportWidth - (tooltipRect?.width || 0) - 20; // Maximum left position
    const minTop = headerHeight + 20; // Minimum top position (header height + margin)
    const maxTop = viewportHeight - (tooltipRect?.height || 0) - 20; // Maximum top position

    // Adjust left position to respect sidebar boundary
    if (left < minLeft) left = minLeft;
    if (left > maxLeft) left = maxLeft;

    // Adjust top position to respect header boundary
    if (top < minTop) {
      // If tooltip would appear above header, try to position it below the target
      if (
        position === "top" &&
        rect.bottom + margin + (tooltipRect?.height || 0) < viewportHeight - 20
      ) {
        top = rect.bottom + margin;
        if (tooltipRect) {
          left = rect.left + rect.width / 2 - tooltipRect.width / 2;
        }
      } else {
        top = minTop;
      }
    }
    if (top > maxTop) top = maxTop;

    return { top, left };
  };

  const position = getTooltipPosition();

  return (
    <>
      {/* Overlay */}
      <div
        className={`${styles.overlay} ${
          currentStep === 0
            ? styles.overlayWelcome
            : styles.overlayRegular
        }`}
        onClick={handleNext}
      />

      {/* Tooltip */}
      <div
        ref={tooltipRef}
        className={`${styles.tooltip} ${currentStep === 0 ? styles.tooltipWelcome : styles.tooltipRegular}`}
        style={{
          top: `${position.top}px`,
          left: `${position.left}px`,
        }}
      >
        <div
          className={`neumorphic-container ${styles.container} ${
            currentStep === 0 ? styles.containerWelcome : styles.containerRegular
          }`}
        >
          {/* Header */}
          <div className={styles.header}>
            {currentStep === 0 ? (
              // Welcome card - no step indicator
              <div></div>
            ) : (
              // Regular tour steps - show step indicator
              <div className={styles.stepIndicator}>
                <div className={styles.stepInfo}>
                  <div className={styles.stepDot} />
                  <span className={styles.stepText}>
                    Step {currentStep} of {steps.length - 1}
                  </span>
                </div>
                {/* Progress indicator */}
                <div className={styles.progressContainer}>
                  {Array.from({ length: steps.length - 1 }, (_, index) => (
                    <div
                      key={index}
                      className={`${styles.progressBar} ${
                        index < currentStep
                          ? styles.progressBarComplete
                          : styles.progressBarIncomplete
                      }`}
                    />
                  ))}
                </div>
              </div>
            )}
            <button
              onClick={handleSkip}
              className={styles.closeButton}
              title='Skip tour'
            >
              <X className={styles.closeIcon} />
            </button>
          </div>

          {/* Content */}
          <div className={styles.content}>
            {/* Client logo for first step */}
            {currentStep === 0 && currentBrand?.client?.logo && (
              <div className={styles.logoContainer}>
                <img
                  src={currentBrand.client.logo}
                  alt={`${currentBrand.client.name} logo`}
                  className={styles.logo}
                  onError={(e) => {
                    // Hide image if it fails to load
                    e.currentTarget.style.display = "none";
                  }}
                />
              </div>
            )}

            <h3
              className={`${styles.title} ${
                currentStep === 0 ? styles.titleWelcome : styles.titleRegular
              }`}
            >
              {currentStep === 0 && currentBrand?.client?.name
                ? `Welcome to ${currentBrand.client.name}`
                : step.title}
            </h3>
            <div
              className={`${styles.description} ${
                currentStep === 0 ? styles.descriptionWelcome : styles.descriptionRegular
              }`}
            >
              {(currentStep === 0 && currentBrand?.client?.firstName
                ? step.content.replace(
                    "{firstName}",
                    currentBrand.client.firstName
                  )
                : step.content.replace("{firstName}", "there")
              )
                .split("\n")
                .map((line, index) => (
                  <p key={index} className={index > 0 ? styles.descriptionParagraph : ""}>
                    {currentStep === 0 ? renderHighlightedText(line) : line}
                  </p>
                ))}
            </div>
          </div>

          {/* Actions */}
          <div className={styles.actions}>
            {currentStep === 0 ? (
              // Welcome card - only show Start Tour button centered
              <div className={styles.actionsSpacer}></div>
            ) : (
              // Regular tour steps - show Previous button
              <button
                onClick={handlePrevious}
                disabled={currentStep === 0}
                className={`${styles.button} ${
                  currentStep === 0
                    ? styles.buttonPreviousDisabled
                    : styles.buttonPrevious
                }`}
              >
                <ChevronLeft className={`${styles.buttonIcon} ${styles.buttonIconLeft}`} />
                Previous
              </button>
            )}

            <div className={styles.actionsRight}>
              {currentStep === 0 ? (
                // Welcome card - Start Tour button
                <button
                  onClick={handleNext}
                  className={`${styles.button} ${styles.buttonStart}`}
                >
                  Start Tour
                  <ChevronRight className={`${styles.buttonIcon} ${styles.buttonIconRight}`} />
                </button>
              ) : currentStep < steps.length - 1 ? (
                // Regular tour steps - Next button
                <button
                  onClick={handleNext}
                  className={`${styles.button} ${styles.buttonNext}`}
                >
                  Next
                  <ChevronRight className={`${styles.buttonIcon} ${styles.buttonIconRight}`} />
                </button>
              ) : (
                // Final step - Complete Tour button
                <button
                  onClick={handleNext}
                  className={`${styles.button} ${styles.buttonComplete}`}
                >
                  Complete Tour
                  <ChevronRight className={`${styles.buttonIcon} ${styles.buttonIconRight}`} />
                </button>
              )}
            </div>

            {currentStep === 0 ? (
              // Welcome card - empty div for balance
              <div className={styles.actionsSpacer}></div>
            ) : null}
          </div>
        </div>

        {/* Arrow */}
        <div
          className={`${styles.arrow} ${
            step.position === "top"
              ? styles.arrowTop
              : step.position === "bottom"
              ? styles.arrowBottom
              : step.position === "left"
              ? styles.arrowLeft
              : styles.arrowRight
          }`}
        />
      </div>
    </>
  );
}
