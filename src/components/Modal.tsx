import React, { useEffect, useState, useId } from "react";
import { X } from "lucide-react";
import { useOverlayState } from "../utils/overlayState";
import { useScrollManager } from "../utils/scrollManager";
import styles from "./Modal.module.css";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  showCloseButton?: boolean;
  className?: string;
}

export function Modal({
  isOpen,
  onClose,
  title,
  children,
  showCloseButton = true,
  className = "",
}: ModalProps) {
  const [isAnimating, setIsAnimating] = useState(false);
  const modalId = useId(); // Generate unique ID for this modal instance
  
  // Register this modal with the overlay system
  useOverlayState(isOpen);
  
  // Use the scroll manager to preserve scroll position (Modal is detected as regular)
  useScrollManager(modalId, isOpen, 'Modal');

  useEffect(() => {
    if (isOpen) {
      setIsAnimating(true);
    }
  }, [isOpen]);

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleClose = () => {
    setIsAnimating(false);
    // Small delay to allow animation to complete
    setTimeout(() => {
      onClose();
    }, 150);
  };

  if (!isOpen) return null;

  return (
    <div
      className={`${styles.modalOverlay} ${
        isAnimating ? styles.open : styles.closed
      }`}
      aria-labelledby='modal-title'
      role='dialog'
      aria-modal='true'
    >
      {/* Backdrop */}
      <div
        className={styles.modalBackdrop}
        onClick={handleBackdropClick}
      />

      {/* Modal Container */}
      <div className={styles.modalContainer}>
        <div
          className={`${styles.modalContent} ${
            isAnimating ? styles.animatingIn : styles.animatingOut
          } ${className}`}
        >
          {/* Header */}
          {(title || showCloseButton) && (
            <div className={styles.modalHeader}>
              {title && (
                <h2 className={styles.modalTitle}>{title}</h2>
              )}
              {showCloseButton && (
                <button
                  onClick={handleClose}
                  className={styles.modalCloseButton}
                  aria-label='Close modal'
                >
                  <X className={styles.modalCloseIcon} />
                </button>
              )}
            </div>
          )}

          {/* Content */}
          <div className={styles.modalBody}>{children}</div>
        </div>
      </div>
    </div>
  );
}
