/* Highlighted text styles */
.highlightedText {
  font-weight: bold;
  color: rgb(147 197 253); /* text-blue-300 */
  background-color: rgb(30 58 138 / 0.2); /* bg-blue-900/20 */
  padding-left: 0.25rem; /* px-1 */
  padding-right: 0.25rem;
  border-radius: 0.25rem; /* rounded */
}

/* Overlay styles */
.overlay {
  position: fixed;
  inset: 0;
  background-color: black;
  z-index: 40;
}

.overlayWelcome {
  background-color: rgb(0 0 0 / 0.5); /* bg-opacity-50 */
  backdrop-filter: blur(24px); /* backdrop-blur-xl */
}

.overlayRegular {
  background-color: rgb(0 0 0 / 0.3); /* bg-opacity-30 */
  backdrop-filter: blur(4px); /* backdrop-blur-sm */
}

/* Tooltip container */
.tooltip {
  position: fixed;
  z-index: 50;
}

.tooltipWelcome {
  max-width: 42rem; /* max-w-2xl */
}

.tooltipRegular {
  max-width: 24rem; /* max-w-sm */
}

/* Neumorphic container */
.container {
  box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25); /* shadow-2xl */
}

.containerWelcome {
  padding: 3rem; /* p-12 */
}

.containerRegular {
  padding: 1.5rem; /* p-6 */
}

/* Header styles */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem; /* mb-4 */
}

.stepIndicator {
  display: flex;
  flex-direction: column;
  gap: 0.5rem; /* space-y-2 */
}

.stepInfo {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* space-x-2 */
}

.stepDot {
  width: 0.5rem; /* w-2 */
  height: 0.5rem; /* h-2 */
  background-color: rgb(59 130 246); /* bg-blue-500 */
  border-radius: 9999px; /* rounded-full */
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.stepText {
  font-size: 0.875rem; /* text-sm */
  font-weight: 600; /* font-semibold */
  color: rgb(209 213 219); /* text-gray-300 */
}

/* Progress indicator */
.progressContainer {
  display: flex;
  gap: 0.25rem; /* space-x-1 */
}

.progressBar {
  height: 0.5rem; /* h-2 */
  width: 1rem; /* w-4 */
  border-radius: 0.125rem; /* rounded-sm */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* transition-all duration-300 ease-in-out */
}

.progressBarComplete {
  background-color: rgb(34 197 94); /* bg-green-500 */
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05); /* shadow-sm */
}

.progressBarIncomplete {
  background-color: rgb(209 213 219); /* bg-gray-300 */
}

/* Close button */
.closeButton {
  padding: 0.25rem; /* p-1 */
  border-radius: 9999px; /* rounded-full */
  transition: background-color 0.15s cubic-bezier(0.4, 0, 0.2, 1); /* transition-colors */
}

.closeButton:hover {
  background-color: rgb(243 244 246); /* hover:bg-gray-100 */
}

.closeIcon {
  width: 1rem; /* w-4 */
  height: 1rem; /* h-4 */
  color: rgb(107 114 128); /* text-gray-500 */
}

/* Content styles */
.content {
  margin-bottom: 1rem; /* mb-4 */
}

.logoContainer {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem; /* mb-6 */
}

.logo {
  height: 5rem; /* h-20 */
  width: auto;
  max-width: 16rem; /* max-w-64 */
  object-fit: contain;
  border-radius: 0.5rem; /* rounded-lg */
}

.title {
  font-weight: bold;
  color: rgb(209 213 219); /* text-gray-300 */
  margin-bottom: 0.5rem; /* mb-2 */
}

.titleWelcome {
  font-size: 1.5rem; /* text-2xl */
  text-align: center;
  margin-bottom: 1.5rem; /* mb-6 */
  filter: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06)); /* drop-shadow-sm */
}

.titleRegular {
  font-size: 1.125rem; /* text-lg */
}

.description {
  color: rgb(243 244 246); /* text-gray-100 */
  line-height: 1.625; /* leading-relaxed */
  font-weight: 500; /* font-medium */
}

.descriptionWelcome {
  font-size: 1rem; /* text-base */
  text-align: left;
  letter-spacing: 0.025em; /* tracking-wide */
  line-height: 1.75; /* leading-7 */
}

.descriptionRegular {
  font-size: 0.875rem; /* text-sm */
}

.descriptionParagraph {
  margin-top: 1rem; /* mt-4 */
}

/* Actions container */
.actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.actionsSpacer {
  flex: 1;
}

.actionsRight {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* space-x-2 */
}

/* Button styles */
.button {
  display: flex;
  align-items: center;
  font-size: 0.875rem; /* text-sm */
  font-weight: 500; /* font-medium */
  border-radius: 0.5rem; /* rounded-lg */
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1); /* transition-colors */
}

.buttonPrevious {
  padding: 0.5rem 0.75rem; /* px-3 py-2 */
  color: rgb(229 231 235); /* text-gray-200 */
}

.buttonPrevious:hover {
  color: rgb(17 24 39); /* hover:text-gray-900 */
  background-color: rgb(243 244 246); /* hover:bg-gray-100 */
}

.buttonPreviousDisabled {
  color: rgb(229 231 235); /* text-gray-200 */
  cursor: not-allowed;
}

.buttonStart {
  padding: 0.5rem 1.5rem; /* px-6 py-2 */
  background-color: rgb(37 99 235); /* bg-blue-600 */
  color: white;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); /* shadow-lg */
}

.buttonStart:hover {
  background-color: rgb(29 78 216); /* hover:bg-blue-700 */
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); /* hover:shadow-xl */
}

.buttonNext {
  padding: 0.5rem 1rem; /* px-4 py-2 */
  background-color: rgb(37 99 235); /* bg-blue-600 */
  color: white;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); /* shadow-lg */
}

.buttonNext:hover {
  background-color: rgb(29 78 216); /* hover:bg-blue-700 */
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); /* hover:shadow-xl */
}

.buttonComplete {
  padding: 0.5rem 1rem; /* px-4 py-2 */
  background-color: rgb(22 163 74); /* bg-green-600 */
  color: white;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); /* shadow-lg */
}

.buttonComplete:hover {
  background-color: rgb(21 128 61); /* hover:bg-green-700 */
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1); /* hover:shadow-xl */
}

.buttonIcon {
  width: 1rem; /* w-4 */
  height: 1rem; /* h-4 */
}

.buttonIconLeft {
  margin-right: 0.25rem; /* mr-1 */
}

.buttonIconRight {
  margin-left: 0.25rem; /* ml-1 */
}

/* Arrow styles */
.arrow {
  position: absolute;
  width: 0.75rem; /* w-3 */
  height: 0.75rem; /* h-3 */
  background-color: white;
  transform: rotate(45deg);
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1); /* shadow-lg */
}

.arrowTop {
  top: 100%;
  margin-top: -0.375rem; /* -mt-1.5 */
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
}

.arrowBottom {
  bottom: 100%;
  margin-bottom: -0.375rem; /* -mb-1.5 */
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
}

.arrowLeft {
  left: 100%;
  margin-left: -0.375rem; /* -ml-1.5 */
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
}

.arrowRight {
  right: 100%;
  margin-right: -0.375rem; /* -mr-1.5 */
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
}
