import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, ArrowRight } from "lucide-react";
import styles from "../pages/PersonaList.module.css";

interface PersonaCarouselLayoutProps {
  children: React.ReactNode;
  total: number;
  current: number;
  onPrev: () => void;
  onNext: () => void;
  onDotClick: (index: number) => void;
  personaCategories: string[];
}

// Layout-only carousel for Customer Persona section
export function PersonaCarouselLayout({
  children,
  total,
  current,
  onPrev,
  onNext,
  onDotClick,
  personaCategories,
}: PersonaCarouselLayoutProps) {
  // 3D carousel parameters
  const cardWidth = 700; // Increased from 500px for much wider selected persona
  const radius = 800; // Increased from 700px for better spacing
  const step = 360 / total; // degrees between cards

  // Calculate rotation angle for the carousel
  const rotation = -current * step;

  return (
    <div className={styles.carouselContainer}>
      {/* 3D Carousel Container */}
      <div
        className={styles.carousel3D}
        style={{
          width: `${cardWidth}px`,
          height: "500px", // Reduced from 600px for slightly shorter height
          perspective: "1600px", // Increased perspective for better 3D effect
        }}
      >
        <div
          className={styles.carouselInner}
          style={{
            transform: `translateZ(-${radius}px) rotateY(${rotation}deg)`,
          }}
        >
          {React.Children.map(children, (child, idx) => {
            // Calculate each card's angle
            const angle = idx * step;
            // Only render cards near the front for performance
            const visible =
              Math.abs(((idx - current + total) % total) - total) <= 2 ||
              Math.abs(idx - current) <= 2;

            const isSelected = idx === current;

            return (
              <div
                key={idx}
                className={styles.carouselItem}
                style={{
                  transform: `rotateY(${angle}deg) translateZ(${radius}px)`,
                  opacity: isSelected ? 1 : 0.2, // Reduced opacity for non-selected
                  filter: isSelected ? "none" : "blur(6px)", // Increased blur for non-selected
                  zIndex: isSelected ? 3 : 1, // Higher z-index for selected
                  pointerEvents: isSelected ? "auto" : "none",
                  transition:
                    "opacity 0.5s, filter 0.5s, z-index 0.5s, transform 0.7s cubic-bezier(0.77,0,0.18,1)",
                  display: visible ? "flex" : "none",
                }}
              >
                <div
                  className={`w-full h-full flex items-center justify-center ${
                    isSelected
                      ? "scale-110" // Make selected persona 10% bigger
                      : "scale-85" // Make non-selected personas smaller
                  }`}
                  style={{
                    transition: "transform 0.5s cubic-bezier(0.77,0,0.18,1)",
                  }}
                >
                  {isSelected ? (
                    // Glassy container for selected persona - much wider with dynamic color-coded gradient
                    <div
                      className={`w-full h-full flex flex-col items-center justify-center p-8 rounded-3xl persona-overview-card ${
                        personaCategories && personaCategories[idx]
                          ? personaCategories[idx]
                              .toLowerCase()
                              .replace(/\s+/g, "-")
                          : "ambitious"
                      }`}
                      style={{
                        minWidth: "650px", // Ensure minimum width for wide container
                      }}
                    >
                      {child}
                    </div>
                  ) : (
                    // Regular container for non-selected personas
                    <div className={styles.carouselItemRegular}>
                      {child}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Navigation arrows (far left/right, vertically centered) */}
      <button
        onClick={onPrev}
        className={`${styles.carouselNavButton} ${styles.carouselNavLeft}`}
        aria-label='Previous Persona'
      >
        <ArrowLeft className={styles.carouselNavIcon} />
      </button>
      <button
        onClick={onNext}
        className={`${styles.carouselNavButton} ${styles.carouselNavRight}`}
        aria-label='Next Persona'
      >
        <ArrowRight className={styles.carouselNavIcon} />
      </button>

      {/* Pagination dots (centered below) */}
      <div className={styles.carouselDots}>
        {Array.from({ length: total }).map((_, idx) => (
          <button
            key={idx}
            onClick={() => onDotClick(idx)}
            className={`${styles.carouselDot} ${
              idx === current
                ? styles.carouselDotActive
                : styles.carouselDotInactive
            }`}
            aria-label={`Go to persona ${idx + 1}`}
            style={{
              backdropFilter: "blur(10px)",
            }}
          />
        ))}
      </div>
    </div>
  );
}
