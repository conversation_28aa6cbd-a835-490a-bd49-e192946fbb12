import { HelpCircle } from "lucide-react";
import { resetTourCompletion } from "../services/tourService";
import { useTour } from "../contexts/TourContext";
import { useLocation } from "react-router-dom";
import styles from "./HelpButton.module.css";

export function HelpButton() {
  const { startMainTour, startAdminTour } = useTour();
  const location = useLocation();

  const handleClick = () => {
    // Reset tour completion status
    resetTourCompletion();

    // Determine which tour to start based on current route
    if (location.pathname === "/admin") {
      startAdminTour();
    } else {
      startMainTour();
    }
  };

  return (
    <button
      onClick={handleClick}
      className={styles.helpButton}
      data-tour='help-button'
    >
      <HelpCircle className={styles.icon} />
    </button>
  );
}
