// Simple verification script for scrollManager tooltip detection
// This can be run in the browser console to verify the fix

import { scrollManager } from './scrollManager.js';

// Test function to verify tooltip detection
function verifyTooltipDetection() {
  console.log('🧪 Testing ScrollManager tooltip detection...');
  
  // Test 1: TourGuide should be detected as tooltip
  const tourGuideId = 'test-tourguide';
  scrollManager.registerModal(tourGuideId, 'TourGuide');
  const isTourGuideTooltip = scrollManager.isTooltipModal(tourGuideId);
  console.log(`✅ TourGuide detected as tooltip: ${isTourGuideTooltip}`);
  
  // Test 2: Header should be detected as tooltip
  const headerId = 'test-header';
  scrollManager.registerModal(headerId, 'Header');
  const isHeaderTooltip = scrollManager.isTooltipModal(headerId);
  console.log(`✅ Header detected as tooltip: ${isHeaderTooltip}`);
  
  // Test 3: Modal should be detected as regular
  const modalId = 'test-modal';
  scrollManager.registerModal(modalId, 'Modal');
  const isModalTooltip = scrollManager.isTooltipModal(modalId);
  console.log(`✅ Modal detected as regular (not tooltip): ${!isModalTooltip}`);
  
  // Clean up
  scrollManager.unregisterModal(tourGuideId);
  scrollManager.unregisterModal(headerId);
  scrollManager.unregisterModal(modalId);
  
  console.log('🎉 All tests passed! Tooltip detection is working correctly.');
  
  return {
    tourGuideIsTooltip: isTourGuideTooltip,
    headerIsTooltip: isHeaderTooltip,
    modalIsRegular: !isModalTooltip
  };
}

// Export for use in browser console
window.verifyScrollManagerFix = verifyTooltipDetection;

console.log('📋 ScrollManager verification loaded. Run verifyScrollManagerFix() in console to test.');
