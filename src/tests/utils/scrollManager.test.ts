// Test file for smart scroll manager functionality
// This file can be used to verify the scroll manager works correctly

import { scrollManager } from './scrollManager';

// Mock window object for testing
const mockWindow = {
  scrollX: 0,
  scrollY: 100,
  scrollTo: jest.fn(),
};

// Mock document object for testing
const mockDocument = {
  body: {
    style: {
      cssText: '',
      position: '',
      top: '',
      left: '',
      right: '',
      overflow: '',
    },
  },
};

// Test the smart scroll manager functionality
describe('Smart ScrollManager', () => {
  beforeEach(() => {
    // Reset the scroll manager state
    scrollManager.clearAll();
    
    // Reset mocks
    jest.clearAllMocks();
  });

  test('should detect regular modals and prevent scrolling', () => {
    const modalId = 'test-modal-1';
    
    // Simulate current scroll position
    Object.defineProperty(window, 'scrollY', {
      value: 150,
      writable: true,
    });
    
    // Register regular modal
    scrollManager.registerModal(modalId, 'Modal');
    
    // Check if modal is registered as regular type
    expect(scrollManager.getActiveModalCount()).toBe(1);
    expect(scrollManager.hasActiveModals()).toBe(true);
    expect(scrollManager.isTooltipModal(modalId)).toBe(false);
    
    // Check if scroll position was saved
    const savedPosition = scrollManager.getScrollPosition(modalId);
    expect(savedPosition).toEqual({ scrollX: 0, scrollY: 150 });
  });

  test('should detect tooltip modals and allow scrolling', () => {
    const modalId = 'test-tooltip-1';

    // Simulate current scroll position
    Object.defineProperty(window, 'scrollY', {
      value: 200,
      writable: true,
    });

    // Register tooltip modal
    scrollManager.registerModal(modalId, 'TourGuide');

    // Check if modal is registered as tooltip type
    expect(scrollManager.getActiveModalCount()).toBe(1);
    expect(scrollManager.hasActiveModals()).toBe(true);
    expect(scrollManager.isTooltipModal(modalId)).toBe(true);

    // Check if scroll position was saved
    const savedPosition = scrollManager.getScrollPosition(modalId);
    expect(savedPosition).toEqual({ scrollX: 0, scrollY: 200 });
  });

  test('should detect Header tooltip as tooltip type', () => {
    const modalId = 'test-header-tooltip';

    // Simulate current scroll position
    Object.defineProperty(window, 'scrollY', {
      value: 300,
      writable: true,
    });

    // Register Header tooltip modal
    scrollManager.registerModal(modalId, 'Header');

    // Check if modal is registered as tooltip type
    expect(scrollManager.getActiveModalCount()).toBe(1);
    expect(scrollManager.hasActiveModals()).toBe(true);
    expect(scrollManager.isTooltipModal(modalId)).toBe(true);

    // Check if scroll position was saved
    const savedPosition = scrollManager.getScrollPosition(modalId);
    expect(savedPosition).toEqual({ scrollX: 0, scrollY: 300 });
  });

  test('should handle multiple modals of different types', () => {
    const regularModalId = 'test-regular-modal';
    const tooltipModalId = 'test-tooltip-modal';
    
    // Register regular modal
    scrollManager.registerModal(regularModalId, 'Modal');
    expect(scrollManager.getActiveModalCount()).toBe(1);
    expect(scrollManager.isTooltipModal(regularModalId)).toBe(false);
    
    // Register tooltip modal
    scrollManager.registerModal(tooltipModalId, 'TourGuide');
    expect(scrollManager.getActiveModalCount()).toBe(2);
    expect(scrollManager.isTooltipModal(tooltipModalId)).toBe(true);
    
    // Unregister regular modal
    scrollManager.unregisterModal(regularModalId);
    expect(scrollManager.getActiveModalCount()).toBe(1);
    expect(scrollManager.hasActiveModals()).toBe(true);
    
    // Unregister tooltip modal
    scrollManager.unregisterModal(tooltipModalId);
    expect(scrollManager.getActiveModalCount()).toBe(0);
    expect(scrollManager.hasActiveModals()).toBe(false);
  });

  test('should detect modal types correctly', () => {
    // Test regular modal detection
    expect(scrollManager['detectModalType']('Modal')).toBe('regular');
    expect(scrollManager['detectModalType']('ClientModal')).toBe('regular');
    expect(scrollManager['detectModalType']('PersonaList')).toBe('regular');
    expect(scrollManager['detectModalType']('Timeline')).toBe('regular');
    expect(scrollManager['detectModalType']('Podcasts')).toBe('regular');
    expect(scrollManager['detectModalType']('Videos')).toBe('regular');
    expect(scrollManager['detectModalType']('Team')).toBe('regular');
    expect(scrollManager['detectModalType']('Admin')).toBe('regular');
    expect(scrollManager['detectModalType']('PersonaGroups')).toBe('regular');
    expect(scrollManager['detectModalType']('ClientHeader')).toBe('regular');
    expect(scrollManager['detectModalType']('Header')).toBe('regular');
    
    // Test tooltip modal detection
    expect(scrollManager['detectModalType']('TourGuide')).toBe('tooltip');
    
    // Test fallback for unknown components
    expect(scrollManager['detectModalType']('UnknownComponent')).toBe('regular');
    expect(scrollManager['detectModalType']()).toBe('regular');
  });

  test('should clear all modal states', () => {
    const modalId1 = 'test-modal-5';
    const modalId2 = 'test-modal-6';
    
    // Register multiple modals
    scrollManager.registerModal(modalId1, 'Modal');
    scrollManager.registerModal(modalId2, 'TourGuide');
    expect(scrollManager.getActiveModalCount()).toBe(2);
    
    // Clear all
    scrollManager.clearAll();
    expect(scrollManager.getActiveModalCount()).toBe(0);
    expect(scrollManager.hasActiveModals()).toBe(false);
  });
});

// Export for use in other test files
export { scrollManager };



