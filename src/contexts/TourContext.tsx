import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
} from "react";

interface TourContextType {
  showMainTour: boolean;
  showAdminTour: boolean;
  startMainTour: () => void;
  startAdminTour: () => void;
  closeMainTour: () => void;
  closeAdminTour: () => void;
}

const TourContext = createContext<TourContextType | undefined>(undefined);

interface TourProviderProps {
  children: ReactNode;
}

export function TourProvider({ children }: TourProviderProps) {
  const [showMainTour, setShowMainTour] = useState(false);
  const [showAdminTour, setShowAdminTour] = useState(false);

  const startMainTour = useCallback(() => {
    setShowMainTour(true);
  }, []);

  const startAdminTour = useCallback(() => {
    setShowAdminTour(true);
  }, []);

  const closeMainTour = useCallback(() => {
    setShowMainTour(false);
  }, []);

  const closeAdminTour = useCallback(() => {
    setShowAdminTour(false);
  }, []);

  const value: TourContextType = {
    showMainTour,
    showAdminTour,
    startMainTour,
    startAdminTour,
    closeMainTour,
    closeAdminTour,
  };

  return (
    <TourContext.Provider value={value}>{children}</TourContext.Provider>
  );
}

export function useTour() {
  const context = useContext(TourContext);
  if (context === undefined) {
    throw new Error("useTour must be used within a TourProvider");
  }
  return context;
}
