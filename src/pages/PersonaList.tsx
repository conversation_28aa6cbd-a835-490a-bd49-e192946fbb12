import React, { useState, useId } from "react";
import { User, Info, Lightbulb, X, ArrowRight, ArrowLeft } from "lucide-react";
import { PersonaDetail } from "./PersonaDetail"; // Import PersonaDetail component
import Slider from "react-slick";
import { PersonaCard } from "../components/PersonaCard";
import { NeumorphicContainer } from "../components/NeumorphicContainer";
import Masonry from "@mui/lab/Masonry";
import { PersonaInfo } from "../components/PersonaInfo";
import { useOverlayState } from "../utils/overlayState";
import { useScrollManager } from "../utils/scrollManager";
import styles from "./PersonaList.module.css";

// Import data from separate files
import {
  categories,
  tips,
  personaOverviews,
  getTitle,
  getSubtitle,
  getFilteredPersonas,
} from "../data/personaListData";
import { sliderSettings } from "../data/sliderConfig";

// Layout-only carousel for Customer Persona section
export function PersonaCarouselLayout({
  children,
  total,
  current,
  onPrev,
  onNext,
  onDotClick,
  personaCategories,
}) {
  // 3D carousel parameters
  const cardWidth = 700; // Increased from 500px for much wider selected persona
  const radius = 800; // Increased from 700px for better spacing
  const step = 360 / total; // degrees between cards

  // Calculate rotation angle for the carousel
  const rotation = -current * step;

  return (
    <div className={styles.carouselContainer}>
      {/* 3D Carousel Container */}
      <div
        className={styles.carousel3D}
        style={{
          width: `${cardWidth}px`,
          height: "500px", // Reduced from 600px for slightly shorter height
          perspective: "1600px", // Increased perspective for better 3D effect
        }}
      >
        <div
          className={styles.carouselInner}
          style={{
            transform: `translateZ(-${radius}px) rotateY(${rotation}deg)`,
          }}
        >
          {React.Children.map(children, (child, idx) => {
            // Calculate each card's angle
            const angle = idx * step;
            // Only render cards near the front for performance
            const visible =
              Math.abs(((idx - current + total) % total) - total) <= 2 ||
              Math.abs(idx - current) <= 2;

            const isSelected = idx === current;

            return (
              <div
                key={idx}
                className={styles.carouselItem}
                style={{
                  transform: `rotateY(${angle}deg) translateZ(${radius}px)`,
                  opacity: isSelected ? 1 : 0.2, // Reduced opacity for non-selected
                  filter: isSelected ? "none" : "blur(6px)", // Increased blur for non-selected
                  zIndex: isSelected ? 3 : 1, // Higher z-index for selected
                  pointerEvents: isSelected ? "auto" : "none",
                  transition:
                    "opacity 0.5s, filter 0.5s, z-index 0.5s, transform 0.7s cubic-bezier(0.77,0,0.18,1)",
                  display: visible ? "flex" : "none",
                }}
              >
                <div
                  className={`w-full h-full flex items-center justify-center ${
                    isSelected
                      ? "scale-110" // Make selected persona 10% bigger
                      : "scale-85" // Make non-selected personas smaller
                  }`}
                  style={{
                    transition: "transform 0.5s cubic-bezier(0.77,0,0.18,1)",
                  }}
                >
                  {isSelected ? (
                    // Glassy container for selected persona - much wider with dynamic color-coded gradient
                    <div
                      className={`w-full h-full flex flex-col items-center justify-center p-8 rounded-3xl persona-overview-card ${
                        personaCategories && personaCategories[idx]
                          ? personaCategories[idx]
                              .toLowerCase()
                              .replace(/\s+/g, "-")
                          : "ambitious"
                      }`}
                      style={{
                        minWidth: "650px", // Ensure minimum width for wide container
                      }}
                    >
                      {child}
                    </div>
                  ) : (
                    // Regular container for non-selected personas
                    <div className={styles.carouselItemRegular}>
                      {child}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Navigation arrows (far left/right, vertically centered) */}
      <button
        onClick={onPrev}
        className={`${styles.carouselNavButton} ${styles.carouselNavLeft}`}
        aria-label='Previous Persona'
      >
        <ArrowLeft className={styles.carouselNavIcon} />
      </button>
      <button
        onClick={onNext}
        className={`${styles.carouselNavButton} ${styles.carouselNavRight}`}
        aria-label='Next Persona'
      >
        <ArrowRight className={styles.carouselNavIcon} />
      </button>

      {/* Pagination dots (centered below) */}
      <div className={styles.carouselDots}>
        {Array.from({ length: total }).map((_, idx) => (
          <button
            key={idx}
            onClick={() => onDotClick(idx)}
            className={`${styles.carouselDot} ${
              idx === current
                ? styles.carouselDotActive
                : styles.carouselDotInactive
            }`}
            aria-label={`Go to persona ${idx + 1}`}
            style={{
              backdropFilter: "blur(10px)",
            }}
          />
        ))}
      </div>
    </div>
  );
}

export function PersonaList({
  onPersonaClick,
}: {
  onPersonaClick: (id: string) => void;
}) {
  const [selectedCategory, setSelectedCategory] = React.useState("All");

  // State for managing persona detail modal
  const [selectedPersonaId, setSelectedPersonaId] = React.useState<
    string | null
  >(null);
  const [currentTip, setCurrentTip] = useState(0);
  const [currentPersona, setCurrentPersona] = useState(0);
  const modalId = useId(); // Generate unique ID for this modal instance
  
  // Register persona modal with overlay system
  useOverlayState(selectedPersonaId !== null);
  
  // Use the scroll manager to preserve scroll position (PersonaList modal is detected as regular)
  useScrollManager(modalId, selectedPersonaId !== null, 'PersonaList');

  const filteredPersonas = getFilteredPersonas(selectedCategory);

  // Handle persona card click - opens modal instead of navigating to separate page
  const handlePersonaClick = (id: string) => {
    setSelectedPersonaId(id);
    // Don't call onPersonaClick to avoid navigation - show modal instead
  };

  const closeModal = () => {
    setSelectedPersonaId(null);
  };

  const nextTip = () => setCurrentTip((prev) => (prev + 1) % tips.length);
  const prevPersona = () =>
    setCurrentPersona(
      (prev) => (prev - 1 + personaOverviews.length) % personaOverviews.length
    );
  const nextPersona = () =>
    setCurrentPersona((prev) => (prev + 1) % personaOverviews.length);

  if (filteredPersonas.length === 0) {
    return (
      <div className={styles.container}>
        <div className={styles.filterSection}>
          {/* Existing header and category buttons */}
          <div className={styles.header}>
            <h2 className={styles.title}>
              {getTitle(selectedCategory)}
            </h2>
            <p className={styles.subtitle}>{getSubtitle(selectedCategory)}</p>
          </div>
          <div className={styles.filterGrid}>
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`${styles.filterButton} ${
                  selectedCategory === category ? styles.filterButtonActive : ""
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        <div className={styles.noResults}>
          <User className={styles.noResultsIcon} />
          <div>
            <h3 className={styles.noResultsTitle}>
              No personas found
            </h3>
            <p className={styles.noResultsText}>
              No personas match the selected category. Try selecting a different
              category.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2 className={styles.title}>Customer Personas</h2>
        <p className={styles.subtitle}>
          Discover the diverse customer segments and their unique characteristics
        </p>
      </div>

      <NeumorphicContainer>
        {/* Tips Section */}
        <div className={`neumorphic-elevated ${styles.tipsContainer}`}>
          <span className={`neumorphic-elevated ${styles.tipLabel}`}>
            Tip
          </span>
          <div className={styles.tipContent}>
            <div className={styles.tipTitle}>
              {tips[currentTip].title}
            </div>
            <div className={styles.tipText}>
              {tips[currentTip].content}
            </div>
          </div>
          <div className={styles.tipNavigation}>
            <button
              onClick={nextTip}
              className={styles.tipButton}
              aria-label='Next Tip'
            >
              <ArrowRight className={styles.tipButtonIcon} />
            </button>
            <span className={styles.tipCounter}>
              {currentTip + 1} of {tips.length}
            </span>
          </div>
        </div>

        {/* Persona Overview Carousel */}
        <PersonaCarouselLayout
          total={personaOverviews.length}
          current={currentPersona}
          onPrev={prevPersona}
          onNext={nextPersona}
          onDotClick={setCurrentPersona}
          personaCategories={personaOverviews.map((persona) => persona.title)}
        >
          {personaOverviews.map((persona, idx) => (
            <div
              key={idx}
              className={styles.personaOverviewItem}
            >
              {persona.image && (
                <img
                  src={persona.image}
                  alt={persona.title}
                  className={`object-cover rounded-2xl mb-6 mx-auto transition-all duration-500 ${
                    idx === currentPersona
                      ? "w-32 h-32 shadow-2xl" // Bigger image for selected
                      : "w-20 h-20 shadow-lg" // Smaller image for others
                  }`}
                />
              )}
              {/* Only show text content for the selected persona */}
              {idx === currentPersona && (
                <>
                  <div
                    className={`font-bold text-center mb-4 transition-all duration-500 ${
                      idx === currentPersona
                        ? "text-2xl text-text-primary" // Bigger, darker text for selected
                        : "text-lg text-text-secondary" // Smaller, lighter text for others
                    }`}
                  >
                    {persona.title}
                  </div>
                  <div
                    className={`text-left px-6 transition-all duration-500 ${
                      idx === currentPersona
                        ? "text-base text-text-primary leading-relaxed" // Bigger, more readable for selected
                        : "text-sm text-text-secondary leading-normal" // Smaller for others
                    }`}
                  >
                    {persona.description.length > 200
                      ? `${persona.description.substring(0, 200)}...`
                      : persona.description}
                  </div>
                </>
              )}
            </div>
          ))}
        </PersonaCarouselLayout>
      </NeumorphicContainer>

      <NeumorphicContainer>
        <h2 className={styles.sectionTitle}>{getTitle(selectedCategory)}</h2>
        <p className={styles.subtitle}>{getSubtitle(selectedCategory)}</p>

        {/* Carousel container with increased padding to prevent hover scaling clipping */}
        {/* - py-8/10/12: Increased vertical padding to accommodate hover:scale-[1.02] effect */}
        {/* - Cards are 500px tall, 2% scale = ~10px growth, so we need extra padding */}
        <div className={styles.sliderContainer}>
          <Slider {...sliderSettings}>
            {filteredPersonas.map((persona) => (
              <div key={persona.id} className={styles.sliderItem}>
                <PersonaCard
                  persona={persona}
                  handlePersonaClick={handlePersonaClick}
                />
              </div>
            ))}
          </Slider>
        </div>
      </NeumorphicContainer>

      {selectedPersonaId && (
        <div
          className={styles.modalBackdrop}
          onClick={closeModal}
        >
          {/* Exit Button - Outermost component for easy access */}
          <button
            onClick={closeModal}
            className={styles.modalCloseButton}
          >
            <X className={styles.modalCloseIcon} />
          </button>

          <div
            className={styles.modal}
            onClick={(e) => e.stopPropagation()}
          >
            <div className={styles.modalContent}>
              <PersonaDetail personaId={selectedPersonaId} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
