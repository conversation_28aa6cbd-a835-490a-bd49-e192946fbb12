import React from 'react';
import { TrendingUp, TrendingDown, FileText, DollarSign, Briefcase, Target } from 'lucide-react';
import styles from './ClientAdmin.module.css';

export function AnalyticsPreview() {
  return (
    <div className={styles.analyticsInterface}>
      <div className={styles.analyticsHeader}>
        <div className={styles.headerTitle}>Dashboard Overview</div>
        <div className={styles.timeFilter}>
          <select className={styles.filterSelect}>
            <option>Last 30 days</option>
            <option>Last 90 days</option>
            <option>Last year</option>
          </select>
        </div>
      </div>
      
      <div className={styles.analyticsGrid}>
        <div className={styles.analyticsCard}>
          <div className={styles.cardHeader}>
            <DollarSign className={styles.cardIcon} />
            <div className={styles.cardTitle}>Contract Value</div>
          </div>
          <div className={styles.cardValue}>$847K</div>
          <div className={styles.cardChange}>
            <TrendingUp className={styles.changeIcon} />
            <span>+18% vs last month</span>
          </div>
        </div>

        <div className={styles.analyticsCard}>
          <div className={styles.cardHeader}>
            <Target className={styles.cardIcon} />
            <div className={styles.cardTitle}>Sales Closed</div>
          </div>
          <div className={styles.cardValue}>12</div>
          <div className={styles.cardChange}>
            <TrendingUp className={styles.changeIcon} />
            <span>+3 vs last month</span>
          </div>
        </div>

        <div className={styles.analyticsCard}>
          <div className={styles.cardHeader}>
            <FileText className={styles.cardIcon} />
            <div className={styles.cardTitle}>Research Projects</div>
          </div>
          <div className={styles.cardValue}>8</div>
          <div className={styles.cardChange}>
            <TrendingUp className={styles.changeIcon} />
            <span>+2 this month</span>
          </div>
        </div>

        <div className={styles.analyticsCard}>
          <div className={styles.cardHeader}>
            <Briefcase className={styles.cardIcon} />
            <div className={styles.cardTitle}>Projects in Progress</div>
          </div>
          <div className={styles.cardValue}>15</div>
          <div className={styles.cardChange}>
            <TrendingDown className={styles.changeIcon} />
            <span>-2 vs last month</span>
          </div>
        </div>
      </div>
      
      <div className={styles.chartSection}>
        <div className={styles.chartHeader}>
          <div className={styles.chartTitle}>Contract Value Trend</div>
          <div className={styles.chartPeriod}>Last 6 months</div>
        </div>
        <div className={styles.chartContainer}>
          <div className={styles.chartBars}>
            <div className={styles.chartBar} style={{height: '55%'}}>
              <div className={styles.barLabel}>Jul</div>
            </div>
            <div className={styles.chartBar} style={{height: '70%'}}>
              <div className={styles.barLabel}>Aug</div>
            </div>
            <div className={styles.chartBar} style={{height: '45%'}}>
              <div className={styles.barLabel}>Sep</div>
            </div>
            <div className={styles.chartBar} style={{height: '85%'}}>
              <div className={styles.barLabel}>Oct</div>
            </div>
            <div className={styles.chartBar} style={{height: '75%'}}>
              <div className={styles.barLabel}>Nov</div>
            </div>
            <div className={styles.chartBar} style={{height: '100%'}}>
              <div className={styles.barLabel}>Dec</div>
            </div>
          </div>
          <div className={styles.chartValues}>
            <span>$620K</span>
            <span>$780K</span>
            <span>$520K</span>
            <span>$920K</span>
            <span>$850K</span>
            <span>$1.1M</span>
          </div>
        </div>
      </div>
      
      <div className={styles.insightsList}>
        <div className={styles.insightItem}>
          <div className={styles.insightIcon}>💡</div>
          <div className={styles.insightText}>
            <strong>Peak Performance:</strong> December shows highest contract value at $1.1M
          </div>
        </div>
        <div className={styles.insightItem}>
          <div className={styles.insightIcon}>📈</div>
          <div className={styles.insightText}>
            <strong>Project Growth:</strong> Research projects increased by 33% this quarter
          </div>
        </div>
        <div className={styles.insightItem}>
          <div className={styles.insightIcon}>🎯</div>
          <div className={styles.insightText}>
            <strong>Opportunity:</strong> 5 proposals pending approval worth $650K total
          </div>
        </div>
      </div>
    </div>
  );
}
