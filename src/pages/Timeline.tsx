import React, { useState, useId } from "react";
import { Check, X, ChevronLeft, ChevronRight } from "lucide-react";
import { motion } from "framer-motion";
import { NeumorphicContainer } from "../components/NeumorphicContainer";
import { useOverlayState } from "../utils/overlayState";
import { useScrollManager, forceRestoreScroll } from "../utils/scrollManager";
import styles from "./Timeline.module.css";

// Import timeline data and utilities
import {
  timelineEvents,
  getFirstRowEvents,
  getSecondRowEvents,
} from "../data/timelineData";



export function Timeline() {
  const [selectedEvent, setSelectedEvent] = useState<number | null>(null);
  const modalId = useId(); // Generate unique ID for this modal instance
  const firstRow = getFirstRowEvents();
  const secondRow = getSecondRowEvents();

  // Register timeline modal with overlay system
  useOverlayState(selectedEvent !== null);

  // Use the scroll manager to preserve scroll position (Timeline modal is detected as regular)
  useScrollManager(modalId, selectedEvent !== null, 'Timeline');

  const openModal = (index: number) => setSelectedEvent(index);
  const closeModal = () => {
    setSelectedEvent(null);
  };
  const showPrev = () =>
    setSelectedEvent((prev) =>
      prev !== null ? (prev - 1 + timelineEvents.length) % timelineEvents.length : null
    );
  const showNext = () =>
    setSelectedEvent((prev) =>
      prev !== null ? (prev + 1) % timelineEvents.length : null
    );

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      closeModal();
    }
  };

  // Handle escape key
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && selectedEvent !== null) {
        closeModal();
      }
    };

    if (selectedEvent !== null) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [selectedEvent]);

  // Safety cleanup on unmount
  React.useEffect(() => {
    return () => {
      // Force restore scroll as a safety measure on component unmount
      forceRestoreScroll();
    };
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2 className={styles.title}>
          Research Timeline
        </h2>
        <p className={styles.subtitle}>
          Project Completion: <b>71%</b>
        </p>
      </div>

      <NeumorphicContainer>
        <div className={styles.timelineContainer}>
          {/* First row - Left to right */}
          <div className={styles.firstRow}>
            {firstRow.map((event, index) => (
              <React.Fragment key={event.phase}>
                <motion.div
                  className={styles.timelineEvent}
                  whileHover={{ scale: 1.05 }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div
                    className={`${styles.eventNode} ${
                      event.completed
                        ? styles.eventNodeCompleted
                        : event.current
                        ? styles.eventNodeCurrent
                        : styles.eventNodeUpcoming
                    }`}
                  >
                    {event.completed && (
                      <Check className={styles.eventIcon} />
                    )}
                    {event.current && (
                      <div className={styles.eventCurrentDot} />
                    )}
                  </div>
                  <button
                    onClick={() => openModal(index)}
                    className={styles.eventButton}
                  >
                    <p className={styles.eventPhase}>
                      {event.phase}
                    </p>
                    <p className={styles.eventDate}>{event.date}</p>
                  </button>
                </motion.div>
                {index < firstRow.length - 1 && (
                  <div
                    className={
                      event.current && firstRow[index + 1]?.upcoming
                        ? styles.connectorDotted
                        : styles.connector
                    }
                  />
                )}
              </React.Fragment>
            ))}
          </div>

          {/* Second row - Right to left */}
          <div className={styles.secondRow}>
            <div className={styles.verticalConnector} />
            {secondRow.map((event, index) => (
              <React.Fragment key={event.phase}>
                <motion.div
                  className={styles.timelineEvent}
                  whileHover={{ scale: 1.05 }}
                  initial={{ opacity: 0, y: 0 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: (index + firstRow.length) * 0.1 }}
                >
                  <div
                    className={`${styles.eventNode} ${
                      event.completed
                        ? styles.eventNodeCompleted
                        : event.current
                        ? styles.eventNodeCurrent
                        : styles.eventNodeUpcoming
                    }`}
                  >
                    {event.completed && (
                      <Check className={styles.eventIcon} />
                    )}
                    {event.current && (
                      <div className={styles.eventCurrentDot} />
                    )}
                  </div>
                  <button
                    onClick={() => openModal(index + firstRow.length)}
                    className={styles.secondRowEventButton}
                  >
                    <p className={styles.eventPhase}>
                      {event.phase}
                    </p>
                    <p className={styles.eventDate}>{event.date}</p>
                  </button>
                </motion.div>
                {index < secondRow.length - 1 && (
                  <div
                    className={
                      event.current && secondRow[index + 1]?.upcoming
                        ? styles.connectorDotted
                        : styles.connector
                    }
                  />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      </NeumorphicContainer>

      {/* Modal for timeline event details with carousel */}
      {selectedEvent !== null && (
        <div className={styles.modalBackdrop} onClick={handleBackdropClick}>
          {/* Backdrop */}
          <div
            className={styles.modalOverlay}
          />

          {/* Modal Container */}
          <div
            className={styles.modal}
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={closeModal}
              className={styles.modalCloseButton}
              aria-label='Close'
            >
              <X className={styles.modalCloseIcon} />
            </button>

            <div className={styles.modalContent}>
              <h3 className={styles.modalTitle}>
                {timelineEvents[selectedEvent].phase}
              </h3>
              <p className={styles.modalDate}>
                {timelineEvents[selectedEvent].date}
              </p>

              <div className={styles.modalDetails}>
                {timelineEvents[selectedEvent].details.map(
                  (detail, index) => (
                    <div
                      key={index}
                      className={styles.modalDetailItem}
                    >
                      <div className={styles.modalDetailBullet} />
                      <span className={styles.modalDetailText}>{detail}</span>
                    </div>
                  )
                )}
              </div>
            </div>

            <div className={styles.modalNavigation}>
              <button
                onClick={showPrev}
                className={styles.navButton}
                aria-label='Previous'
              >
                <ChevronLeft className={styles.navIcon} />
              </button>

              <div className={styles.eventIndicator}>
                {selectedEvent + 1} of {timelineEvents.length}
              </div>

              <button
                onClick={showNext}
                className={styles.navButton}
                aria-label='Next'
              >
                <ChevronRight className={styles.navIcon} />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
