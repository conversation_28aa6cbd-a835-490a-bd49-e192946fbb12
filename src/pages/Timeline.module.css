/* Timeline Page Styles */

.container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
  padding: var(--spacing-2);
}

.header {
  text-align: left;
  margin-bottom: var(--spacing-6);
}

.title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.subtitle {
  color: var(--color-text-secondary);
  margin-top: var(--spacing-2);
  font-size: var(--font-size-lg);
}

/* Timeline Container */
.timelineContainer {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-16);
}

/* First Row - Left to Right */
.firstRow {
  display: flex;
  align-items: center;
  padding-left: var(--spacing-12);
  padding-right: var(--spacing-12);
  padding-top: var(--spacing-16);
}

.timelineEvent {
  position: relative;
  display: flex;
  align-items: center;
}

.eventNode {
  width: var(--spacing-6);
  height: var(--spacing-6);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 10;
  flex-shrink: 0;
}

.eventNodeCompleted {
  background: linear-gradient(to right, #4ade80, #22c55e);
  box-shadow: 0 4px 6px -1px rgba(34, 197, 94, 0.1);
}

.eventNodeCurrent {
  background: linear-gradient(to right, #60a5fa, #3b82f6);
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1);
}

.eventNodeUpcoming {
  background: white;
  border: 2px solid var(--color-gray-300);
}

.eventIcon {
  height: var(--spacing-4);
  width: var(--spacing-4);
  color: white;
}

.eventCurrentDot {
  width: var(--spacing-2);
  height: var(--spacing-2);
  border-radius: var(--radius-full);
  background: white;
}

.eventButton {
  position: absolute;
  top: -64px;
  left: 50%;
  transform: translateX(-50%);
  width: 128px;
  text-align: center;
  background: none;
  border: none;
  cursor: pointer;
}

.eventPhase {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  transition: color var(--transition-base);
}

.eventButton:hover .eventPhase {
  color: var(--color-text-accent);
}

.eventDate {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  margin-top: var(--spacing-1);
}

.connector {
  height: 3px;
  background: linear-gradient(to right, #4ade80, #22c55e);
  flex: 1;
  margin: 0;
}

.connectorDotted {
  height: 3px;
  border-bottom: 3px dotted white;
  background: none;
  flex: 1;
  margin: 0;
}

/* Second Row - Right to Left */
.secondRow {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  position: relative;
  padding-left: var(--spacing-12);
  padding-right: var(--spacing-12);
  padding-bottom: var(--spacing-16);
}

.verticalConnector {
  position: absolute;
  right: 59px;
  top: -75px;
  width: 3px;
  background: linear-gradient(to bottom, #22c55e, #3b82f6);
  height: 75px;
}

.secondRowEventButton {
  position: absolute;
  bottom: -64px;
  left: 50%;
  transform: translateX(-50%);
  width: 128px;
  text-align: center;
  background: none;
  border: none;
  cursor: pointer;
}

/* Modal Styles */
.modalBackdrop {
  position: fixed;
  inset: 0;
  z-index: var(--z-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
  animation: backdropFadeIn 0.4s ease-out;
}

.modalOverlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(12px) saturate(1.8);
  -webkit-backdrop-filter: blur(12px) saturate(1.8);
  cursor: pointer;
  pointer-events: auto;
}

.modal {
  position: relative;
  max-width: 520px;
  width: 100%;
  min-height: 480px;
  margin: var(--spacing-4);
  padding: var(--spacing-8);
  border-radius: var(--radius-3xl);

  /* Glassmorphism effect */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  backdrop-filter: blur(20px) saturate(1.8);
  -webkit-backdrop-filter: blur(20px) saturate(1.8);
  border: 1px solid rgba(255, 255, 255, 0.2);

  /* Enhanced shadow system */
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);

  /* Spring animation */
  animation: modalSpringIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-origin: center center;

  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.modalContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 400px;
}

.modalCloseButton {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  padding: var(--spacing-3);
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.modalCloseButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modalCloseButton:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.modalCloseIcon {
  height: var(--spacing-5);
  width: var(--spacing-5);
  color: rgba(255, 255, 255, 0.9);
  transition: color 0.2s ease;
}

.modalCloseButton:hover .modalCloseIcon {
  color: rgba(255, 255, 255, 1);
}

.modalTitle {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: rgba(255, 255, 255, 0.95);
  margin-bottom: var(--spacing-2);
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  letter-spacing: -0.025em;
}

.modalDate {
  font-size: var(--font-size-lg);
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-6);
  text-align: center;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.modalDetails {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
  flex: 1;
  width: 100%;
  max-width: 360px;
}

.modalDetailItem {
  display: flex;
  align-items: flex-start;
  text-align: left;
}

.modalDetailBullet {
  height: var(--spacing-2);
  width: var(--spacing-2);
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.8);
  margin-top: var(--spacing-2);
  margin-right: var(--spacing-3);
  flex-shrink: 0;
}

.modalDetailText {
  color: rgba(255, 255, 255, 0.85);
  line-height: 1.6;
  font-size: var(--font-size-base);
}

/* Modal Navigation */
.modalNavigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-top: auto;
  padding-top: var(--spacing-4);
}

.navButton {
  padding: var(--spacing-3);
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--spacing-12);
  height: var(--spacing-12);
}

.navButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.navButton:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

.navIcon {
  height: var(--spacing-5);
  width: var(--spacing-5);
  color: rgba(255, 255, 255, 0.9);
  transition: color 0.2s ease;
}

.navButton:hover .navIcon {
  color: rgba(255, 255, 255, 1);
}

.eventIndicator {
  color: rgba(255, 255, 255, 0.7);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Responsive Design */
@media (max-width: 768px) {
  .firstRow,
  .secondRow {
    padding: 0 var(--spacing-4);
  }

  .eventButton,
  .secondRowEventButton {
    width: 96px;
  }

  .eventPhase {
    font-size: var(--font-size-xs);
  }

  .modal {
    margin: var(--spacing-3);
    padding: var(--spacing-8);
    min-height: 400px;
    max-width: calc(100vw - var(--spacing-6));
  }

  .modalTitle {
    font-size: var(--font-size-2xl);
  }

  .modalDate {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-4);
  }

  .modalDetails {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-6);
  }

  .navButton {
    width: var(--spacing-10);
    height: var(--spacing-10);
  }

  .navIcon {
    height: var(--spacing-4);
    width: var(--spacing-4);
  }

  .eventIndicator {
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 480px) {
  .modal {
    margin: var(--spacing-2);
    padding: var(--spacing-6);
    min-height: 360px;
  }

  .modalTitle {
    font-size: var(--font-size-xl);
  }

  .navButton {
    width: var(--spacing-8);
    height: var(--spacing-8);
  }

  .navIcon {
    height: var(--spacing-3);
    width: var(--spacing-3);
  }
}

/* Animation Classes */
@keyframes backdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSpringIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  60% {
    opacity: 1;
    transform: scale(1.02) translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.fadeIn {
  animation: fadeIn 0.3s ease-out;
}

.scaleIn {
  animation: scaleIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
