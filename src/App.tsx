import React, { useState, useEffect } from "react";
import {
  Routes,
  Route,
  Navigate,
  useLocation,
} from "react-router-dom";
import { Layout } from "./components/Layout";
import { PinAccess } from "./components/PinAccess";
import { isAdminAuthenticated } from "./utils/auth";
import { Admin } from "./pages/Admin";
import { HelpButton } from "./components/HelpButton";
import BackgroundImage from "./components/BackgroundImage";
import { brandApiService, type Brand } from "./services/brandApiService";
import { TourGuide } from "./components/TourGuide";
import {
  shouldShowTour,
  getTourSteps,
  markTourCompleted,
} from "./services/tourService";
import { ModalProvider } from "./contexts/ModalContext";
import { TourProvider, useTour } from "./contexts/TourContext";
import "./styles/tour.css";
import styles from "./App.module.css";

function AppContent() {
  const [adminAuthenticated, setAdminAuthenticated] = useState(() =>
    isAdminAuthenticated()
  );
  const [defaultBrand, setDefaultBrand] = useState<{ endpoint: string } | null>(
    null
  );
  const [currentBrand, setCurrentBrand] = useState<Brand | null>(null);
  const [loading, setLoading] = useState(true);
  const location = useLocation();
  const { showAdminTour, startAdminTour, closeAdminTour } = useTour();

  const handlePinSuccess = () => {
    // Update authentication state to trigger re-render
    setAdminAuthenticated(true);
  };

  // Sync authentication state with session storage
  useEffect(() => {
    const checkAuth = () => {
      setAdminAuthenticated(isAdminAuthenticated());
    };

    // Check on mount
    checkAuth();

    // Listen for storage changes (in case of multiple tabs)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "adminAuthenticated") {
        checkAuth();
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, []);

  // Load default brand for root redirect
  useEffect(() => {
    const loadDefaultBrand = async () => {
      try {
        const brand = await brandApiService.getDefaultBrand();
        setDefaultBrand(brand);
      } catch (error) {
        console.error("Error loading default brand:", error);
        // Fallback to a basic brand object
        setDefaultBrand({ endpoint: "balsam" });
      } finally {
        setLoading(false);
      }
    };

    loadDefaultBrand();
  }, []);

  // Load current brand based on URL for tour
  useEffect(() => {
    const loadCurrentBrand = async () => {
      try {
        // Extract brand name from URL path
        const pathParts = location.pathname.split("/");
        const brandName = pathParts[1]; // First part after domain

        if (brandName && brandName !== "admin") {
          const brand = await brandApiService.getBrandByEndpoint(brandName);
          if (brand) {
            setCurrentBrand(brand);
          } else {
            // Fallback to default brand
            const defaultBrand = await brandApiService.getDefaultBrand();
            setCurrentBrand(defaultBrand);
          }
        } else {
          // No brand in URL, use default brand
          const defaultBrand = await brandApiService.getDefaultBrand();
          setCurrentBrand(defaultBrand);
        }
      } catch (error) {
        console.error("Error loading current brand for tour:", error);
        // Fallback to a basic brand object
        setCurrentBrand({
          id: "fallback",
          name: "Project Roots",
          logo: "",
          endpoint: "fallback",
          clientId: "",
          isDefault: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      }
    };

    loadCurrentBrand();
  }, [location.pathname]);

  // Check if admin tour should be shown
  useEffect(() => {
    if (location.pathname === "/admin") {
      // Only show admin tour if authenticated
      if (adminAuthenticated && shouldShowTour("admin")) {
        startAdminTour();
      }
    }
  }, [location.pathname, adminAuthenticated, startAdminTour]);

  const handleAdminTourComplete = () => {
    closeAdminTour();
    markTourCompleted("admin");
  };

  return (
    <ModalProvider>
      <BackgroundImage />
      <Routes>
        {/* Root redirect to default brand */}
        <Route
          path='/'
          element={
            loading ? (
              <div className={styles.loadingContainer}>
                <div className={styles.loadingContent}>
                  <div className={styles.spinner}></div>
                  <p className={styles.loadingText}>Loading...</p>
                </div>
              </div>
            ) : (
              <Navigate to={`/${defaultBrand?.endpoint || "balsam"}/overview`} replace />
            )
          }
        />

        {/* Brand-specific routes - accessible without PIN */}
        <Route path='/:brandName/*' element={<Layout />} />

        {/* Admin route - requires PIN verification */}
        <Route
          path='/admin'
          element={
            adminAuthenticated ? (
              <Admin />
            ) : (
              <PinAccess onSuccess={handlePinSuccess} />
            )
          }
        />
      </Routes>
      <HelpButton />

      {/* Admin Tour Guide */}
      {showAdminTour && (
        <TourGuide
          steps={getTourSteps("admin")}
          isOpen={showAdminTour}
          onClose={closeAdminTour}
          onComplete={handleAdminTourComplete}
          tourId='admin'
          currentBrand={currentBrand}
        />
      )}
    </ModalProvider>
  );
}

function App() {
  return (
    <TourProvider>
      <AppContent />
    </TourProvider>
  );
}

export default App;
